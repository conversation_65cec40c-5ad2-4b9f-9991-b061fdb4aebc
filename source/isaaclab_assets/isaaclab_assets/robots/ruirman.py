# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Configuration for the Ruirman Emika robots.

The following configurations are available:

* :obj:`RUIRMAN_PANDA_CFG`: Ruirman Emika Panda robot with Panda hand
* :obj:`RUIRMAN_PANDA_HIGH_PD_CFG`: Ruirman Emika Panda robot with Panda hand with stiffer PD control

Reference: https://github.com/frankaemika/franka_ros
"""

import isaaclab.sim as sim_utils
from isaaclab.actuators import ImplicitActuatorCfg
from isaaclab.assets.articulation import ArticulationCfg
# from isaaclab.utils.assets import ISAACLAB_NUCLEUS_DIR

##
# Configuration
##

RUIRMAN_PANDA_CFG = ArticulationCfg(
    spawn=sim_utils.UsdFileCfg(
        # usd_path=f"{ISAACLAB_NUCLEUS_DIR}/Robots/FrankaEmika/panda_instanceable.usd",
        usd_path="/home/<USER>/IsaacLab/usd_files/rm_75_with_eg2_sf16/rm_75_with_eg2_sf16.usd",
        activate_contact_sensors=False,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            max_depenetration_velocity=5.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=True, 
            solver_position_iteration_count=8, 
            solver_velocity_iteration_count=0
        ),
        # collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.005, rest_offset=0.0),
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        joint_pos={
            "joint1": 0.0,
            "joint2": 0.0,
            "joint3": 0.0,
            "joint4": 1.97,
            "joint5": 0.0,
            "joint6": 1.17,
            "joint7": 1.57,
            "joint_gripper": 0.0,
            "finger_joint.*": 0.05,
        },
    ),
    actuators={
        "panda_shoulder": ImplicitActuatorCfg(
            joint_names_expr=["joint[1-2]"],
            effort_limit_sim=180.0,
            velocity_limit_sim=3.141,
            stiffness=200.0,
            damping=40.0,
        ),
        "panda_upperarm": ImplicitActuatorCfg(
            joint_names_expr=["joint[3-4]"],
            effort_limit_sim=89.79,
            velocity_limit_sim=3.927,
            stiffness=100.0,
            damping=20.0,
        ),
        "panda_forearm": ImplicitActuatorCfg(
            joint_names_expr=["joint[5-7]"],
            effort_limit_sim=30.0,
            velocity_limit_sim=3.927,
            stiffness=80.0,
            damping=16.0,
        ),
        "panda_hand": ImplicitActuatorCfg(
            joint_names_expr=["joint_gripper", "finger_joint.*"],
            effort_limit_sim=200.0,
            velocity_limit_sim=0.2,
            stiffness=2e3,
            damping=1e2,
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)
"""Configuration of Ruirman Emika Panda robot."""


RUIRMAN_PANDA_HIGH_PD_CFG = RUIRMAN_PANDA_CFG.copy()
RUIRMAN_PANDA_HIGH_PD_CFG.spawn.rigid_props.disable_gravity = True
RUIRMAN_PANDA_HIGH_PD_CFG.actuators["panda_shoulder"].stiffness = 1000.0
RUIRMAN_PANDA_HIGH_PD_CFG.actuators["panda_shoulder"].damping = 200.0
RUIRMAN_PANDA_HIGH_PD_CFG.actuators["panda_upperarm"].stiffness = 1000.0
RUIRMAN_PANDA_HIGH_PD_CFG.actuators["panda_upperarm"].damping = 200.0
RUIRMAN_PANDA_HIGH_PD_CFG.actuators["panda_forearm"].stiffness = 500.0
RUIRMAN_PANDA_HIGH_PD_CFG.actuators["panda_forearm"].damping = 100.0
"""Configuration of Ruirman Emika Panda robot with stiffer PD control.

This configuration is useful for task-space control using differential IK.
"""
