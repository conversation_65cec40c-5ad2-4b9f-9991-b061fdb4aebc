{"algo_name": "bc", "experiment": {"name": "bc_rnn_image_franka_stack", "validate": false, "logging": {"terminal_output_to_txt": true, "log_tb": true}, "save": {"enabled": true, "every_n_seconds": null, "every_n_epochs": 20, "epochs": [], "on_best_validation": false, "on_best_rollout_return": false, "on_best_rollout_success_rate": true}, "epoch_every_n_steps": 500, "env": null, "additional_envs": null, "render": false, "render_video": false, "rollout": {"enabled": false}}, "train": {"data": null, "num_data_workers": 4, "hdf5_cache_mode": "low_dim", "hdf5_use_swmr": true, "hdf5_load_next_obs": false, "hdf5_normalize_obs": false, "hdf5_filter_key": null, "hdf5_validation_filter_key": null, "seq_length": 10, "pad_seq_length": true, "frame_stack": 1, "pad_frame_stack": true, "dataset_keys": ["actions", "rewards", "dones"], "goal_mode": null, "cuda": true, "batch_size": 16, "num_epochs": 600, "seed": 101}, "algo": {"optim_params": {"policy": {"optimizer_type": "adam", "learning_rate": {"initial": 0.0001, "decay_factor": 0.1, "epoch_schedule": [], "scheduler_type": "multistep"}, "regularization": {"L2": 0.0}}}, "loss": {"l2_weight": 1.0, "l1_weight": 0.0, "cos_weight": 0.0}, "actor_layer_dims": [], "gaussian": {"enabled": false, "fixed_std": false, "init_std": 0.1, "min_std": 0.01, "std_activation": "softplus", "low_noise_eval": true}, "gmm": {"enabled": true, "num_modes": 5, "min_std": 0.0001, "std_activation": "softplus", "low_noise_eval": true}, "vae": {"enabled": false, "latent_dim": 14, "latent_clip": null, "kl_weight": 1.0, "decoder": {"is_conditioned": true, "reconstruction_sum_across_elements": false}, "prior": {"learn": false, "is_conditioned": false, "use_gmm": false, "gmm_num_modes": 10, "gmm_learn_weights": false, "use_categorical": false, "categorical_dim": 10, "categorical_gumbel_softmax_hard": false, "categorical_init_temp": 1.0, "categorical_temp_anneal_step": 0.001, "categorical_min_temp": 0.3}, "encoder_layer_dims": [300, 400], "decoder_layer_dims": [300, 400], "prior_layer_dims": [300, 400]}, "rnn": {"enabled": true, "horizon": 10, "hidden_dim": 1000, "rnn_type": "LSTM", "num_layers": 2, "open_loop": false, "kwargs": {"bidirectional": false}}, "transformer": {"enabled": false, "context_length": 10, "embed_dim": 512, "num_layers": 6, "num_heads": 8, "emb_dropout": 0.1, "attn_dropout": 0.1, "block_output_dropout": 0.1, "sinusoidal_embedding": false, "activation": "gelu", "supervise_all_steps": false, "nn_parameter_for_timesteps": true}}, "observation": {"modalities": {"obs": {"low_dim": ["eef_pos", "eef_quat", "gripper_pos"], "rgb": ["table_cam", "wrist_cam"], "depth": [], "scan": []}, "goal": {"low_dim": [], "rgb": [], "depth": [], "scan": []}}, "encoder": {"low_dim": {"core_class": null, "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "rgb": {"core_class": "VisualCore", "core_kwargs": {"feature_dimension": 64, "flatten": true, "backbone_class": "ResNet18Conv", "backbone_kwargs": {"pretrained": false, "input_coord_conv": false}, "pool_class": "SpatialSoftmax", "pool_kwargs": {"num_kp": 32, "learnable_temperature": false, "temperature": 1.0, "noise_std": 0.0, "output_variance": false}}, "obs_randomizer_class": "CropRandomizer", "obs_randomizer_kwargs": {"crop_height": 76, "crop_width": 76, "num_crops": 1, "pos_enc": false}}, "depth": {"core_class": "VisualCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "scan": {"core_class": "ScanCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}}}}