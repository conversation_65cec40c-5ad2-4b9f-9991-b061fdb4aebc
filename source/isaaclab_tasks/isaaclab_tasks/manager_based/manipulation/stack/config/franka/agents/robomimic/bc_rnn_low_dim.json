{"algo_name": "bc", "experiment": {"name": "bc_rnn_low_dim_franka_stack", "validate": false, "logging": {"terminal_output_to_txt": true, "log_tb": true}, "save": {"enabled": true, "every_n_seconds": null, "every_n_epochs": 100, "epochs": [], "on_best_validation": false, "on_best_rollout_return": false, "on_best_rollout_success_rate": true}, "epoch_every_n_steps": 100, "env": null, "additional_envs": null, "render": false, "render_video": false, "rollout": {"enabled": false}}, "train": {"data": null, "num_data_workers": 4, "hdf5_cache_mode": "all", "hdf5_use_swmr": true, "hdf5_normalize_obs": false, "hdf5_filter_key": null, "hdf5_validation_filter_key": null, "seq_length": 10, "dataset_keys": ["actions"], "goal_mode": null, "cuda": true, "batch_size": 100, "num_epochs": 2000, "seed": 101}, "algo": {"optim_params": {"policy": {"optimizer_type": "adam", "learning_rate": {"initial": 0.001, "decay_factor": 0.1, "epoch_schedule": [], "scheduler_type": "multistep"}, "regularization": {"L2": 0.0}}}, "loss": {"l2_weight": 1.0, "l1_weight": 0.0, "cos_weight": 0.0}, "actor_layer_dims": [], "gmm": {"enabled": true, "num_modes": 5, "min_std": 0.0001, "std_activation": "softplus", "low_noise_eval": true}, "rnn": {"enabled": true, "horizon": 10, "hidden_dim": 400, "rnn_type": "LSTM", "num_layers": 2, "open_loop": false, "kwargs": {"bidirectional": false}}}, "observation": {"modalities": {"obs": {"low_dim": ["eef_pos", "eef_quat", "gripper_pos", "object"], "rgb": [], "depth": [], "scan": []}}}}