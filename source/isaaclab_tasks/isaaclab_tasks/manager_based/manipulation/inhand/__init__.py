# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""In-hand object reorientation environment.

These environments are based on the `dexterous cube manipulation`_ environments
provided in IsaacGymEnvs repository from NVIDIA. However, they contain certain
modifications and additional features.

.. _dexterous cube manipulation: https://github.com/NVIDIA-Omniverse/IsaacGymEnvs/blob/main/isaacgymenvs/tasks/allegro_hand.py

"""
