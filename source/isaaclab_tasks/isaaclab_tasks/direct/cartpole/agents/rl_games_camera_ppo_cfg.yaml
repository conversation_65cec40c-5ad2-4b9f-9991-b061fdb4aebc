params:
  seed: 42

  # environment wrapper clipping
  env:
    # added to the wrapper
    clip_observations: 5.0
    # can make custom wrapper?
    clip_actions: 1.0

  algo:
    name: a2c_continuous

  model:
    name: continuous_a2c_logstd

  # doesn't have this fine grained control but made it close
  network:
    name: actor_critic
    separate: False
    space:
      continuous:
        mu_activation: None
        sigma_activation: None

        mu_init:
          name: default
        sigma_init:
          name: const_initializer
          val: 0
        fixed_sigma: True
    cnn:
      type: conv2d
      activation: relu
      initializer:
          name: default
      regularizer:
        name: None
      convs:
        - filters: 32
          kernel_size: 8
          strides: 4
          padding: 0
        - filters: 64
          kernel_size: 4
          strides: 2
          padding: 0
        - filters: 64
          kernel_size: 3
          strides: 1
          padding: 0

    mlp:
      units: [512]
      activation: elu
      initializer:
          name: default

  load_checkpoint: False # flag which sets whether to load the checkpoint
  load_path: '' # path to the checkpoint to load

  config:
    name: cartpole_camera_direct
    env_name: rlgpu
    device: 'cuda:0'
    device_name: 'cuda:0'
    multi_gpu: False
    ppo: True
    mixed_precision: False
    normalize_input: False
    normalize_value: True
    num_actors: -1  # configured from the script (based on num_envs)
    reward_shaper:
      scale_value: 1.0
    normalize_advantage: True
    gamma: 0.99
    tau : 0.95
    learning_rate: 1e-4
    lr_schedule: adaptive
    kl_threshold: 0.008
    score_to_win: 20000
    max_epochs: 500
    save_best_after: 50
    save_frequency: 25
    grad_norm: 1.0
    entropy_coef: 0.0
    truncate_grads: True
    e_clip: 0.2
    horizon_length: 64
    minibatch_size: 2048
    mini_epochs: 4
    critic_coef: 2
    clip_value: True
    seq_length: 4
    bounds_loss_coef: 0.0001
