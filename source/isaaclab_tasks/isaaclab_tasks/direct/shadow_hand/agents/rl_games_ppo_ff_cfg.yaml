params:
  seed: 42

  # environment wrapper clipping
  env:
    # added to the wrapper
    clip_observations: 5.0
    # can make custom wrapper?
    clip_actions: 1.0

  algo:
    name: a2c_continuous

  model:
    name: continuous_a2c_logstd

  network:
    name: actor_critic
    separate: False

    space:
      continuous:
        mu_activation: None
        sigma_activation: None
        mu_init:
          name: default
        sigma_init:
          name: const_initializer
          val: 0
        fixed_sigma: True
    mlp:
      units: [400, 400, 200, 100]
      activation: elu
      d2rl: False

      initializer:
        name: default
      regularizer:
        name: None

  load_checkpoint: False # flag which sets whether to load the checkpoint
  load_path: '' # path to the checkpoint to load

  config:
    name: shadow_hand_openai_ff
    env_name: rlgpu
    device: 'cuda:0'
    device_name: 'cuda:0'
    multi_gpu: False
    ppo: True
    mixed_precision: False
    normalize_input: True
    normalize_value: True
    num_actors: -1
    reward_shaper:
      scale_value: 0.01
    normalize_advantage: True
    gamma: 0.998
    tau: 0.95
    learning_rate: 5e-4
    lr_schedule: adaptive
    schedule_type: standard
    kl_threshold: 0.016
    score_to_win: 100000
    max_epochs: 10000
    save_best_after: 100
    save_frequency: 200
    print_stats: True
    grad_norm: 1.0
    entropy_coef: 0.0
    truncate_grads: True
    e_clip: 0.2
    horizon_length: 16
    minibatch_size: 16384
    mini_epochs: 4
    critic_coef: 4
    clip_value: True
    seq_length: 4
    bounds_loss_coef: 0.0001

    central_value_config:
      minibatch_size: 32768
      mini_epochs: 4
      learning_rate: 5e-4
      lr_schedule: adaptive
      schedule_type: standard
      kl_threshold: 0.016
      clip_value: True
      normalize_input: True
      truncate_grads: True

      network:
        name: actor_critic
        central_value: True
        mlp:
          units: [512, 512, 256, 128]
          activation: elu
          d2rl: False
          initializer:
            name: default
          regularizer:
            name: None

    player:
      deterministic: True
      games_num: 100000
      print_stats: True
