[package]

# Note: Semantic Versioning is used: https://semver.org/
version = "0.10.33"

# Description
title = "Isaac Lab Environments"
description="Extension containing suite of environments for robot learning."
readme  = "docs/README.md"
repository = "https://github.com/isaac-sim/IsaacLab"
category = "robotics"
keywords = ["robotics", "rl", "il", "learning"]

[dependencies]
"isaaclab" = {}
"isaaclab_assets" = {}

[core]
reloadable = false

[[python.module]]
name = "isaaclab_tasks"
