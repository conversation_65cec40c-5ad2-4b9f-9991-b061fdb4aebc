# mode for very simple functional testing without checking thresholds
fast_test:
  rl_games:Isaac-Ant-v0:
    max_iterations: 10
    lower_thresholds:
      reward: -99999
      episode_length: -99999
    upper_thresholds:
      duration: 99999

# mode for capturing KPIs across all environments without checking thresholds
full_test:
  Isaac-*:
    max_iterations: 500
    lower_thresholds:
      reward: -99999
      episode_length: -99999
    upper_thresholds:
      duration: 99999

# mode for PR tests (default mode)
fast:
  rl_games:Isaac-Ant-v0:
    max_iterations: 200
    lower_thresholds:
      reward: 45
      episode_length: 900
    upper_thresholds:
      duration: 500
  skrl:Isaac-Cartpole-RGB-Camera-Direct-v0:
    max_iterations: 50
    lower_thresholds:
      reward: 190
      episode_length: 230
    upper_thresholds:
      duration: 450
  rsl_rl:Isaac-Humanoid-v0:
    max_iterations: 200
    lower_thresholds:
      reward: 50
      episode_length: 750
    upper_thresholds:
      duration: 500
  rl_games:Isaac-Quadcopter-Direct-v0:
    max_iterations: 200
    lower_thresholds:
      reward: 100
      episode_length: 400
    upper_thresholds:
      duration: 250
  skrl:Isaac-Shadow-Hand-Over-Direct-v0:
    max_iterations: 300
    lower_thresholds:
      reward: 30
      episode_length: 250
    upper_thresholds:
      duration: 600
  rsl_rl:Isaac-Velocity-Rough-Anymal-C-v0:
    max_iterations: 300
    lower_thresholds:
      reward: 7
      episode_length: 900
    upper_thresholds:
      duration: 1800
  rl_games:Isaac-Repose-Cube-Allegro-Direct-v0:
    max_iterations: 500
    lower_thresholds:
      reward: 200
      episode_length: 150
    upper_thresholds:
      duration: 1500


# mode for weekly CI
full:
  Isaac-Ant-Direct-v0:
    max_iterations: 300
    lower_thresholds:
      reward: 7000
      episode_length: 700
    upper_thresholds:
      duration: 500
  Isaac-Ant-v0:
    max_iterations: 1000
    lower_thresholds:
      reward: 100
      episode_length: 700
    upper_thresholds:
      duration: 800
  Isaac-Cart-Double-Pendulum-Direct-v0:
    max_iterations: 300
    lower_thresholds:
      reward: 400
      episode_length: 150
    upper_thresholds:
      duration: 500
  Isaac-Cartpole-Depth-Camera-Direct-v0:
    max_iterations: 300
    lower_thresholds:
      reward: 200
      episode_length: 150
    upper_thresholds:
      duration: 3000
  Isaac-Cartpole-Depth-v0:
    max_iterations: 300
    lower_thresholds:
      reward: 1
      episode_length: 150
    upper_thresholds:
      duration: 3000
  Isaac-Cartpole-Direct-v0:
    max_iterations: 300
    lower_thresholds:
      reward: 200
      episode_length: 150
    upper_thresholds:
      duration: 500
  Isaac-Cartpole-RGB-Camera-Direct-v0:
    max_iterations: 300
    lower_thresholds:
      reward: 200
      episode_length: 150
    upper_thresholds:
      duration: 3000
  Isaac-Cartpole-RGB-ResNet18-v0:
    max_iterations: 300
    lower_thresholds:
      reward: 1
      episode_length: 100
    upper_thresholds:
      duration: 4000
  Isaac-Cartpole-RGB-TheiaTiny-v0:
    max_iterations: 300
    lower_thresholds:
      reward: 1
      episode_length: 150
    upper_thresholds:
      duration: 4000
  Isaac-Cartpole-RGB-v0:
    max_iterations: 300
    lower_thresholds:
      reward: -2
      episode_length: 150
    upper_thresholds:
      duration: 4000
  Isaac-Cartpole-v0:
    max_iterations: 1000
    lower_thresholds:
      reward: 3
      episode_length: 150
    upper_thresholds:
      duration: 1500
  Isaac-Factory-GearMesh-Direct-v0:
    max_iterations: 100
    lower_thresholds:
      reward: 200
      episode_length: 250
    upper_thresholds:
      duration: 6000
  Isaac-Factory-NutThread-Direct-v0:
    max_iterations: 100
    lower_thresholds:
      reward: 400
      episode_length: 400
    upper_thresholds:
      duration: 5000
  Isaac-Factory-PegInsert-Direct-v0:
    max_iterations: 100
    lower_thresholds:
      reward: 125
      episode_length: 130
    upper_thresholds:
      duration: 4000
  Isaac-Franka-Cabinet-Direct-v0:
    max_iterations: 300
    lower_thresholds:
      reward: 2000
      episode_length: 400
    upper_thresholds:
      duration: 1000
  Isaac-Humanoid-Direct-v0:
    max_iterations: 300
    lower_thresholds:
      reward: 2000
      episode_length: 600
    upper_thresholds:
      duration: 1000
  Isaac-Humanoid-v0:
    max_iterations: 1000
    lower_thresholds:
      reward: 100
      episode_length: 600
    upper_thresholds:
      duration: 2500
  Isaac-Lift-Cube-Franka-v0:
    max_iterations: 300
    lower_thresholds:
      reward: 90
      episode_length: 100
    upper_thresholds:
      duration: 1000
  Isaac-Navigation-Flat-Anymal-C-v0:
    max_iterations: 300
    lower_thresholds:
      reward: 0.5
      episode_length: 20
    upper_thresholds:
      duration: 2000
  Isaac-Open-Drawer-Franka-v0:
    max_iterations: 200
    lower_thresholds:
      reward: 60
      episode_length: 150
    upper_thresholds:
      duration: 3000
  Isaac-Quadcopter-Direct-v0:
    max_iterations: 500
    lower_thresholds:
      reward: 90
      episode_length: 300
    upper_thresholds:
      duration: 500
  Isaac-Reach-Franka-*:
    max_iterations: 1000
    lower_thresholds:
      reward: 0.25
      episode_length: 150
    upper_thresholds:
      duration: 1500
  Isaac-Reach-UR10-v0:
    max_iterations: 1000
    lower_thresholds:
      reward: 0.25
      episode_length: 150
    upper_thresholds:
      duration: 1500
  Isaac-Repose-Cube-Allegro-Direct-v0:
    max_iterations: 500
    lower_thresholds:
      reward: 200
      episode_length: 150
    upper_thresholds:
      duration: 1500
  Isaac-Repose-Cube-Allegro-*:
    max_iterations: 500
    lower_thresholds:
      reward: 15
      episode_length: 300
    upper_thresholds:
      duration: 1500
  Isaac-Repose-Cube-Shadow-Direct-v0:
    max_iterations: 3000
    lower_thresholds:
      reward: 1000
      episode_length: 300
    upper_thresholds:
      duration: 10000
  Isaac-Repose-Cube-Shadow-OpenAI-FF-Direct-v0:
    max_iterations: 3000
    lower_thresholds:
      reward: 1000
      episode_length: 50
    upper_thresholds:
      duration: 15000
  Isaac-Repose-Cube-Shadow-OpenAI-LSTM-Direct-v0:
    max_iterations: 3000
    lower_thresholds:
      reward: 1000
      episode_length: 100
    upper_thresholds:
      duration: 30000
  Isaac-Repose-Cube-Shadow-Vision-Direct-v0:
    max_iterations: 3000
    lower_thresholds:
      reward: 1000
      episode_length: 400
    upper_thresholds:
      duration: 40000
  Isaac-Shadow-Hand-Over-Direct-v0:
    max_iterations: 3000
    lower_thresholds:
      reward: 1000
      episode_length: 150
    upper_thresholds:
      duration: 10000
  Isaac-Velocity-Flat-*:
    max_iterations: 1000
    lower_thresholds:
      reward: 15
      episode_length: 700
    upper_thresholds:
      duration: 3000
  Isaac-Velocity-Flat-Spot-v0:
    max_iterations: 1000
    lower_thresholds:
      reward: 150
      episode_length: 700
    upper_thresholds:
      duration: 6000
  Isaac-Velocity-Rough-*:
    max_iterations: 1000
    lower_thresholds:
      reward: 7
      episode_length: 700
    upper_thresholds:
      duration: 6000
