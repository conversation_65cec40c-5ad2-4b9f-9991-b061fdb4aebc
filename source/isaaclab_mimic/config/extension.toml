[package]

# Semantic Versioning is used: https://semver.org/
version = "1.0.7"

# Description
category = "isaaclab"
readme  = "README.md"

title = "Isaac Lab Mimic"
author = "Isaac Lab Project Developers"
maintainer = "Isaac Lab Project Developers"
description="Mimic for Isaac Lab"
repository = "https://github.com/isaac-sim/IsaacLab.git"
keywords = ["extension", "template", "isaaclab"]

[dependencies]
"isaaclab" = {}
"isaaclab_assets" = {}
"isaaclab_tasks" = {}
# NOTE: Add additional dependencies here

[core]
reloadable = false

[[python.module]]
name = "isaaclab_mimic"

[isaaclab_settings]
